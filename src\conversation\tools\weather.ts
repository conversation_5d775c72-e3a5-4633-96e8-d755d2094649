import { DeepSeek, AiMessage } from "../../ai/deepseek";
import { logger } from "../../utils/logger";
import axios from 'axios';

export interface WeatherData {
    location: string;
    temperature: number;
    description: string;
    humidity: number;
    windSpeed: number;
    feelsLike: number;
}

export class WeatherTool {
    private deepseek: DeepSeek;
    private apiKey: string;

    constructor() {
        this.deepseek = new DeepSeek();
        this.apiKey = process.env.OPENWEATHER_API_KEY || "";
        
        if (!this.apiKey) {
            console.warn("⚠️ OPENWEATHER_API_KEY not found in environment variables");
        }
    }

    async shouldUseWeatherTool(message: string, context: AiMessage[]): Promise<boolean> {
        const contextString = context.map(msg => `${msg.role}: ${msg.content}`).join('\n');
        const evaluationPrompt = `
You are evaluating whether a user's message indicates they want weather information.

Look for:
- Direct weather requests ("what's the weather", "how's the weather", "weather forecast")
- Location-based weather queries ("weather in Paris", "is it raining in Tokyo")
- Weather-related questions ("should I bring an umbrella", "is it cold outside")
- Activity planning related to weather ("good day for a picnic", "should I go for a walk")

Ignore:
- General conversation not related to weather
- Non-weather related questions
- Comments about weather that don't require information ("I love sunny days", "it's so hot today")
- Comments about the current weather without a request for information ("it's sunny today", "it's raining now", "it's cold outside")

Message: "${message}"
Recent context: ${contextString}

Respond with only "YES" if the user wants weather information, or "NO" if they don't.
        `.trim();

        const messages: AiMessage[] = [
            { role: "system", content: evaluationPrompt },
            { role: "user", content: message }
        ];

        try {
            const response = await this.deepseek.chatCompletion(messages, "Data Analysis");
            const shouldUse = response.content?.trim().toUpperCase() === "YES";
            logger.weather(`Weather tool evaluation: ${shouldUse ? 'NEEDED' : 'NOT NEEDED'}`, {
                messagePreview: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
                evaluation: response.content?.trim()
            });
            return shouldUse;
        } catch (error) {
            logger.error('WEATHER', 'Error evaluating weather tool usage', error);
            return false;
        }
    }

    async extractLocation(message: string, userMemories: string): Promise<string | null> {
        const extractionPrompt = `
Extract the location from this weather-related message. If no specific location is mentioned, return "current" to indicate the user's current location.

Examples:
- "What's the weather in Paris?" → "Paris"
- "How's the weather?" → "current"
- "Is it raining in New York City?" → "New York City"
- "Weather forecast for London, UK" → "London, UK"

Previous memories about this user:
${userMemories}

Message: "${message}"

Respond with only the location name or "current":
        `.trim();

        const messages: AiMessage[] = [
            { role: "system", content: extractionPrompt },
            { role: "user", content: message }
        ];

        try {
            const response = await this.deepseek.chatCompletion(messages, "Data Analysis");
            const location = response.content?.trim();
            const result = location === "current" ? null : (location || null);

            logger.weather('Location extracted from message', {
                messagePreview: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
                extractedLocation: location,
                finalResult: result || 'current location'
            });

            return result;
        } catch (error) {
            logger.error('WEATHER', 'Error extracting location', error);
            return null;
        }
    }

    async getWeatherData(location?: string): Promise<WeatherData | null> {
        if (!this.apiKey) {
            logger.error('WEATHER', 'OpenWeather API key not configured');
            return null;
        }

        logger.weather('Fetching weather data', { requestedLocation: location || 'current/default' });

        try {
            let url: string;

            if (location) {
                // Get weather for specific location
                url = `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(location)}&appid=${this.apiKey}&units=metric`;
            } else {
                // For current location, we'll use a default location or IP-based location
                // For now, let's use a default location (you can enhance this with IP geolocation)
                logger.warn('WEATHER', 'No location specified, using default location (London)');
                url = `https://api.openweathermap.org/data/2.5/weather?q=London&appid=${this.apiKey}&units=metric`;
            }

            const apiStart = Date.now();
            const response = await axios.get(url);
            const apiDuration = Date.now() - apiStart;
            const data = response.data;

            const weatherData = {
                location: `${data.name}, ${data.sys.country}`,
                temperature: Math.round(data.main.temp),
                description: data.weather[0].description,
                humidity: data.main.humidity,
                windSpeed: data.wind.speed,
                feelsLike: Math.round(data.main.feels_like)
            };

            logger.weather(`Weather data retrieved in ${apiDuration}ms`, {
                location: weatherData.location,
                temperature: weatherData.temperature,
                description: weatherData.description
            });

            return weatherData;
        } catch (error) {
            logger.error('WEATHER', 'Error fetching weather data', error, { requestedLocation: location });
            return null;
        }
    }

    formatWeatherForContext(weatherData: WeatherData): string {
        return `Current weather in ${weatherData.location}:
- Temperature: ${weatherData.temperature}°C (feels like ${weatherData.feelsLike}°C)
- Conditions: ${weatherData.description}
- Humidity: ${weatherData.humidity}%
- Wind Speed: ${weatherData.windSpeed} m/s`;
    }

    async processWeatherRequest(message: string, userMemories: string): Promise<WeatherData | null> {
        try {
            // Extract location from the message
            const location = await this.extractLocation(message, userMemories);
            
            // Get weather data
            const weatherData = await this.getWeatherData(location || undefined);
            
            if (weatherData) {
                console.log(`🌤️ Retrieved weather for ${weatherData.location}`);
            }
            
            return weatherData;
        } catch (error) {
            console.error("Error processing weather request:", error);
            return null;
        }
    }
}
