import * as Discord from 'discord-user-bots';
import Client from 'discord-user-bots/src/client/client';

// Define a class to hold our client instance
class ClientSingleton {
  private static instance: Client | null = null;

  // Private constructor prevents direct instantiation
  private constructor() {}

  // Method to set the client instance
  public static setClient(client: Client): void {
    ClientSingleton.instance = client;
  }

  // Method to get the client instance
  public static getClient(): Client {
    if (!ClientSingleton.instance) {
      throw new Error('Discord client not initialized yet! (╥﹏╥)');
    }
    return ClientSingleton.instance;
  }
}

export default ClientSingleton;