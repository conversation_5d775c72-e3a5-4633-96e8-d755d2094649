# 📝 Monika <PERSON>t Logging System

## Overview
The bot now includes a comprehensive logging system that tracks all requests, thinking processes, API calls, and system events with timestamps. A new log file is created each day to keep everything organized.

## Features

### 🎯 **Daily Log Files**
- **Location**: `logs/moni-bot-YYYY-MM-DD.log`
- **Format**: New file created automatically each day
- **Header**: Each file starts with a date header

### 🏷️ **Log Levels & Categories**
- **DEBUG** - Detailed debugging information
- **INFO** - General information and status updates
- **WARN** - Warning messages
- **ERROR** - Error conditions
- **API** - API calls and responses (DeepSeek, OpenWeather)
- **MEMORY** - Memory evaluation and storage operations
- **WEATHER** - Weather tool operations
- **DISCORD** - Discord API interactions

### 🔍 **Request Tracking**
Each user message generates a unique request ID that tracks the entire processing flow:

```
[16:05:45] INFO     REQUEST      🚀 Starting request processing [req:req_123_abc user:user123 ch:channel456]
[16:05:45] API      DEEPSEEK     Generating AI response [req:req_123_abc]
[16:05:45] MEMORY   MEMORY       Evaluating memory worthiness [req:req_123_abc]
[16:05:45] WEATHER  WEATHER      Checking if weather tool should be used [req:req_123_abc]
[16:05:46] INFO     STEP         ✅ AI Response Generation completed in 150ms [req:req_123_abc]
[16:05:46] INFO     REQUEST      🎉 Request completed in 255ms [req:req_123_abc]
```

### 📊 **What Gets Logged**

#### **Message Processing**
- User message received and saved
- Request start/completion with timing
- Step-by-step processing with durations
- Request cancellations when new messages arrive

#### **AI API Calls**
- DeepSeek API requests and responses
- Response generation timing
- Model parameters and token usage
- Error handling and retries

#### **Memory System**
- Memory worthiness evaluations
- Memory creation and storage
- File operations and storage statistics
- Memory trimming when limits are reached

#### **Weather Tool**
- Weather request detection
- Location extraction
- OpenWeather API calls with timing
- Weather data retrieval and formatting

#### **Discord Operations**
- Message sending/receiving
- Typing indicators start/stop
- Channel and user context
- Discord API errors

### 🎨 **Console Output**
The system provides color-coded console output while simultaneously saving to files:
- 🟢 **INFO** - Green
- 🔵 **DEBUG** - Cyan  
- 🟡 **WARN** - Yellow
- 🔴 **ERROR** - Red
- 🟣 **API** - Magenta
- 🔵 **MEMORY** - Blue
- 🟦 **WEATHER** - Bright Cyan
- 🟪 **DISCORD** - Bright Magenta

### 📁 **File Structure**
```
logs/
├── moni-bot-2025-06-24.log
├── moni-bot-2025-06-25.log
└── moni-bot-2025-06-26.log
```

### 🔧 **Usage Examples**

#### **Basic Logging**
```typescript
import { logger } from './src/utils/logger';

logger.info('SYSTEM', 'Bot started successfully');
logger.error('DATABASE', 'Connection failed', error);
```

#### **Request Tracking**
```typescript
import { logger, generateRequestId } from './src/utils/logger';

const requestId = generateRequestId();
logger.startRequest(requestId, userId, channelId, message);
logger.stepComplete(requestId, 'Processing step', duration);
logger.requestComplete(requestId, totalDuration);
```

#### **Specialized Logging**
```typescript
logger.api('DEEPSEEK', 'API call completed', { tokens: 150, duration: 200 });
logger.memory('Memory created', { memoryId: 'mem_123', content: 'User likes pizza' });
logger.weather('Weather data retrieved', { location: 'London', temp: 20 });
logger.discord('Message sent', { channelId: '123', length: 45 });
```

## Benefits

### 🔍 **Complete Traceability**
- Track every user interaction from start to finish
- Debug issues with detailed request flows
- Monitor API performance and response times

### 📈 **Performance Monitoring**
- Measure response times for each processing step
- Identify bottlenecks in the message handling flow
- Track API call durations and success rates

### 🛠️ **Debugging & Troubleshooting**
- Detailed error logging with context
- Request correlation across multiple operations
- Historical data for pattern analysis

### 📊 **Analytics & Insights**
- User interaction patterns
- Memory creation frequency
- Weather tool usage statistics
- System performance metrics

The logging system provides comprehensive visibility into Monika Bot's operations while maintaining excellent performance and organized daily log files!
