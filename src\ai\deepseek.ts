import OpenAI from "openai";

export interface AiMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
}

export class DeepSeek {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
        baseURL: 'https://api.deepseek.com/v1',
        apiKey: process.env.DEEPSEEK_API_KEY || "",
    });
  }

  async chatCompletion(messages: AiMessage[], temperature: "Coding" | "Data Analysis" | "Conversation" | "Translation" | "Poetry" = "Conversation") {
    let te = 1.3;
    if (temperature === "Coding") te = 0;
    else if (temperature === "Data Analysis") te = 1;
    else if (temperature === "Conversation") te = 1.3;
    else if (temperature === "Translation") te = 1.3;
    else if (temperature === "Poetry") te = 1.5;

    const chatCompletion = await this.openai.chat.completions.create({
      messages: messages,
      model: "deepseek-chat",
      temperature: te
    });
    return chatCompletion.choices[0].message;
  }

  async chatCompletionStream(
    messages: AiMessage[],
    temperature: "Coding" | "Data Analysis" | "Conversation" | "Translation" | "Poetry" = "Conversation",
    onChunk?: (chunk: string) => void,
    signal?: AbortSignal
  ): Promise<string> {
    let te = 1.3;
    if (temperature === "Coding") te = 0;
    else if (temperature === "Data Analysis") te = 1;
    else if (temperature === "Conversation") te = 1.3;
    else if (temperature === "Translation") te = 1.3;
    else if (temperature === "Poetry") te = 1.5;

    const stream = await this.openai.chat.completions.create({
      messages: messages,
      model: "deepseek-chat",
      temperature: te,
      stream: true
    }, {
      signal: signal
    });

    let fullContent = '';
    let currentLine = '';

    for await (const chunk of stream) {
      if (signal?.aborted) {
        throw new Error('Request cancelled');
      }

      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        fullContent += content;
        currentLine += content;

        // Check if we have a line break
        if (content.includes('\n')) {
          // Split by line breaks and process each line
          const lines = currentLine.split('\n');

          // Process all complete lines (all but the last one)
          for (let i = 0; i < lines.length - 1; i++) {
            if (onChunk && lines[i].trim()) {
              onChunk(lines[i] + '\n');
            }
          }

          // Keep the last incomplete line for the next iteration
          currentLine = lines[lines.length - 1];
        }
      }
    }

    // Send any remaining content
    if (currentLine.trim() && onChunk) {
      onChunk(currentLine);
    }

    return fullContent;
  }
}