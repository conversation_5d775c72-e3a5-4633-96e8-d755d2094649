import { Conversation } from "../conversation/conversation";

export class DiscordEvents {
    private conversation: Conversation;

    constructor() {
        this.conversation = new Conversation();
    }

    // Official Discord events
    voice_server_update(message: any) {
        console.log('🔊 voice_server_update:', message);
    }

    user_update(message: any) {
        console.log('👤 user_update:', message);
    }

    application_command_create(message: any) {
        console.log('⚡ application_command_create:', message);
    }

    application_command_update(message: any) {
        console.log('⚡ application_command_update:', message);
    }

    application_command_delete(message: any) {
        console.log('⚡ application_command_delete:', message);
    }

    interaction_create(message: any) {
        console.log('🔄 interaction_create:', message);
    }

    guild_create(message: any) {
        console.log('🏰 guild_create:', message);
    }

    guild_delete(message: any) {
        console.log('🏰 guild_delete:', message);
    }

    guild_role_create(message: any) {
        console.log('🎭 guild_role_create:', message);
    }

    guild_role_update(message: any) {
        console.log('🎭 guild_role_update:', message);
    }

    guild_role_delete(message: any) {
        console.log('🎭 guild_role_delete:', message);
    }

    thread_create(message: any) {
        console.log('🧵 thread_create:', message);
    }

    thread_join(message: any) {
        console.log('🧵 thread_join:', message);
    }

    thread_update(message: any) {
        console.log('🧵 thread_update:', message);
    }

    thread_delete(message: any) {
        console.log('🧵 thread_delete:', message);
    }

    thread_list_sync(message: any) {
        console.log('🧵 thread_list_sync:', message);
    }

    thread_member_update(message: any) {
        console.log('🧵 thread_member_update:', message);
    }

    thread_members_update(message: any) {
        console.log('🧵 thread_members_update:', message);
    }

    channel_create(message: any) {
        console.log('📺 channel_create:', message);
    }

    channel_update(message: any) {
        console.log('📺 channel_update:', message);
    }

    channel_delete(message: any) {
        console.log('📺 channel_delete:', message);
    }

    channel_pins_update(message: any) {
        console.log('📌 channel_pins_update:', message);
    }

    guild_member_add(message: any) {
        console.log('👥 guild_member_add:', message);
    }

    guild_member_update(message: any) {
        console.log('👥 guild_member_update:', message);
    }

    guild_member_remove(message: any) {
        console.log('👥 guild_member_remove:', message);
    }

    guild_ban_add(message: any) {
        console.log('🔨 guild_ban_add:', message);
    }

    guild_ban_remove(message: any) {
        console.log('🔨 guild_ban_remove:', message);
    }

    guild_emojis_update(message: any) {
        console.log('😀 guild_emojis_update:', message);
    }

    guild_stickers_update(message: any) {
        console.log('🏷️ guild_stickers_update:', message);
    }

    guild_integrations_update(message: any) {
        console.log('🔗 guild_integrations_update:', message);
    }

    guild_webhooks_update(message: any) {
        console.log('🪝 guild_webhooks_update:', message);
    }

    invite_create(message: any) {
        console.log('📨 invite_create:', message);
    }

    invite_delete(message: any) {
        console.log('📨 invite_delete:', message);
    }

    voice_state_update(message: any) {
        console.log('🔊 voice_state_update:', message);
    }

    presence_update(message: any) {
        console.log('🟢 presence_update:', message);
    }

    message_update(message: any) {
        console.log('✏️ message_update:', message);
    }

    message_delete(message: any) {
        console.log('🗑️ message_delete:', message);
    }

    message_delete_bulk(message: any) {
        console.log('🗑️ message_delete_bulk:', message);
    }

    message_reaction_add(message: any) {
        console.log('👍 message_reaction_add:', message);
    }

    message_reaction_remove(message: any) {
        console.log('👎 message_reaction_remove:', message);
    }

    message_reaction_remove_all(message: any) {
        console.log('🚫 message_reaction_remove_all:', message);
    }

    message_reaction_remove_emoji(message: any) {
        console.log('🚫 message_reaction_remove_emoji:', message);
    }

    typing_start(message: any) {
        console.log('⌨️ typing_start:', message);
    }

    relationship_add(message: any) {
        console.log('👫 relationship_add:', message);
    }

    relationship_remove(message: any) {
        console.log('💔 relationship_remove:', message);
    }

    // Unofficial Discord events
    discord_disconnect() {
        console.log('🔌 discord_disconnect');
    }

    discord_reconnect() {
        console.log('🔌 discord_reconnect');
    }

    gateway(message: any) {
        console.log('🚪 gateway:', message);
    }

    heartbeat_sent() {
        console.log('💓 heartbeat_sent');
    }

    heartbeat_received() {
        console.log('💗 heartbeat_received');
    }

    ready() {
        console.log('✅ ready');
    }

    message(message: any) {
        console.log('💬 message:', message);

        this.conversation.onMessage(message);
    }

    default(message: any) {
        console.log('❓ default:', message);
    }

    recipient_add(message: any) {
        console.log('➕ recipient_add:', message);
    }

    recipient_remove(message: any) {
        console.log('➖ recipient_remove:', message);
    }

    call(message: any) {
        console.log('📞 call:', message);
    }

    channel_name_change(message: any) {
        console.log('🏷️ channel_name_change:', message);
    }

    channel_icon_change(message: any) {
        console.log('🖼️ channel_icon_change:', message);
    }

    channel_pinned_message(message: any) {
        console.log('📌 channel_pinned_message:', message);
    }

    user_join(message: any) {
        console.log('🚪 user_join:', message);
    }

    guild_boost(message: any) {
        console.log('🚀 guild_boost:', message);
    }

    guild_boost_tier_1(message: any) {
        console.log('🚀 guild_boost_tier_1:', message);
    }

    guild_boost_tier_2(message: any) {
        console.log('🚀 guild_boost_tier_2:', message);
    }

    guild_boost_tier_3(message: any) {
        console.log('🚀 guild_boost_tier_3:', message);
    }

    channel_follow_add(message: any) {
        console.log('👁️ channel_follow_add:', message);
    }

    guild_discovery_disqualified(message: any) {
        console.log('🔍 guild_discovery_disqualified:', message);
    }

    guild_discovery_requalified(message: any) {
        console.log('🔍 guild_discovery_requalified:', message);
    }

    guild_discovery_grace_period_initial_warning(message: any) {
        console.log('⚠️ guild_discovery_grace_period_initial_warning:', message);
    }

    guild_discovery_grace_period_final_warning(message: any) {
        console.log('⚠️ guild_discovery_grace_period_final_warning:', message);
    }

    thread_created(message: any) {
        console.log('🧵 thread_created:', message);
    }

    reply(message: any) {
        console.log('↩️ reply:', message);
    }

    chat_input_command(message: any) {
        console.log('💬 chat_input_command:', message);
    }

    thread_starter_message(message: any) {
        console.log('🧵 thread_starter_message:', message);
    }

    guild_invite_reminder(message: any) {
        console.log('📨 guild_invite_reminder:', message);
    }

    context_menu_command(message: any) {
        console.log('📋 context_menu_command:', message);
    }

    auto_moderation_action(message: any) {
        console.log('🤖 auto_moderation_action:', message);
    }

    role_subscription_purchase(message: any) {
        console.log('💰 role_subscription_purchase:', message);
    }

    interaction_premium_upsell(message: any) {
        console.log('💎 interaction_premium_upsell:', message);
    }

    stage_start(message: any) {
        console.log('🎭 stage_start:', message);
    }

    stage_end(message: any) {
        console.log('🎭 stage_end:', message);
    }

    stage_speaker(message: any) {
        console.log('🎤 stage_speaker:', message);
    }

    stage_topic(message: any) {
        console.log('🎭 stage_topic:', message);
    }

    guild_application_premium_subscription(message: any) {
        console.log('💎 guild_application_premium_subscription:', message);
    }
}
