import * as Discord from 'discord-user-bots';
import { DiscordEvents } from './src/events/events.ts';
import 'dotenv/config';
import ClientSingleton from './src/singleton/clientSingleton.ts';

async function main() {
    console.log('🌸 Initializing Monika Bot...');
    console.log('📅 Started at:', new Date().toISOString());

    // Initialize Discord client
    const client = new Discord.Client();

    ClientSingleton.setClient(client);

    // Initialize Discord events handler
    const discordEvents = new DiscordEvents();

    // Set up all event listeners
    setupEventListeners(client, discordEvents);

    // Login with token (you'll need to set this)
    const token = process.env.USER_TOKEN;
    if (!token) {
        console.error('❌ DISCORD_TOKEN environment variable is required');
        process.exit(1);
    }

    try {
        await client.login(token);
        console.log('✅ Discord client logged in successfully');
    } catch (error) {
        console.error('❌ Failed to login to Discord:', error);
        process.exit(1);
    }
}

function setupEventListeners(client: any, events: DiscordEvents) {
    // Official Discord events
    // client.on('voice_server_update', (message: any) => events.voice_server_update(message));
    // client.on('user_update', (message: any) => events.user_update(message));
    // client.on('application_command_create', (message: any) => events.application_command_create(message));
    // client.on('application_command_update', (message: any) => events.application_command_update(message));
    // client.on('application_command_delete', (message: any) => events.application_command_delete(message));
    // client.on('interaction_create', (message: any) => events.interaction_create(message));
    // client.on('guild_create', (message: any) => events.guild_create(message));
    // client.on('guild_delete', (message: any) => events.guild_delete(message));
    // client.on('guild_role_create', (message: any) => events.guild_role_create(message));
    // client.on('guild_role_update', (message: any) => events.guild_role_update(message));
    // client.on('guild_role_delete', (message: any) => events.guild_role_delete(message));
    // client.on('thread_create', (message: any) => events.thread_create(message));
    // client.on('thread_join', (message: any) => events.thread_join(message));
    // client.on('thread_update', (message: any) => events.thread_update(message));
    // client.on('thread_delete', (message: any) => events.thread_delete(message));
    // client.on('thread_list_sync', (message: any) => events.thread_list_sync(message));
    // client.on('thread_member_update', (message: any) => events.thread_member_update(message));
    // client.on('thread_members_update', (message: any) => events.thread_members_update(message));
    // client.on('channel_create', (message: any) => events.channel_create(message));
    // client.on('channel_update', (message: any) => events.channel_update(message));
    // client.on('channel_delete', (message: any) => events.channel_delete(message));
    // client.on('channel_pins_update', (message: any) => events.channel_pins_update(message));
    // client.on('guild_member_add', (message: any) => events.guild_member_add(message));
    // client.on('guild_member_update', (message: any) => events.guild_member_update(message));
    // client.on('guild_member_remove', (message: any) => events.guild_member_remove(message));
    // client.on('guild_ban_add', (message: any) => events.guild_ban_add(message));
    // client.on('guild_ban_remove', (message: any) => events.guild_ban_remove(message));
    // client.on('guild_emojis_update', (message: any) => events.guild_emojis_update(message));
    // client.on('guild_stickers_update', (message: any) => events.guild_stickers_update(message));
    // client.on('guild_integrations_update', (message: any) => events.guild_integrations_update(message));
    // client.on('guild_webhooks_update', (message: any) => events.guild_webhooks_update(message));
    // client.on('invite_create', (message: any) => events.invite_create(message));
    // client.on('invite_delete', (message: any) => events.invite_delete(message));
    // client.on('voice_state_update', (message: any) => events.voice_state_update(message));
    // client.on('presence_update', (message: any) => events.presence_update(message));
    client.on('message_update', (message: any) => events.message_update(message));
    client.on('message_delete', (message: any) => events.message_delete(message));
    client.on('message_delete_bulk', (message: any) => events.message_delete_bulk(message));
    client.on('message_reaction_add', (message: any) => events.message_reaction_add(message));
    client.on('message_reaction_remove', (message: any) => events.message_reaction_remove(message));
    client.on('message_reaction_remove_all', (message: any) => events.message_reaction_remove_all(message));
    client.on('message_reaction_remove_emoji', (message: any) => events.message_reaction_remove_emoji(message));
    client.on('typing_start', (message: any) => events.typing_start(message));
    // client.on('relationship_add', (message: any) => events.relationship_add(message));
    // client.on('relationship_remove', (message: any) => events.relationship_remove(message));

    // Unofficial Discord events
    // client.on('discord_disconnect', () => events.discord_disconnect());
    // client.on('discord_reconnect', () => events.discord_reconnect());
    // client.on('gateway', (message: any) => events.gateway(message));
    // client.on('heartbeat_sent', () => events.heartbeat_sent());
    // client.on('heartbeat_received', () => events.heartbeat_received());
    // client.on('ready', () => events.ready());
    client.on('message', (message: any) => events.message(message));
    // client.on('default', (message: any) => events.default(message));
    // client.on('recipient_add', (message: any) => events.recipient_add(message));
    // client.on('recipient_remove', (message: any) => events.recipient_remove(message));
    // client.on('call', (message: any) => events.call(message));
    // client.on('channel_name_change', (message: any) => events.channel_name_change(message));
    // client.on('channel_icon_change', (message: any) => events.channel_icon_change(message));
    // client.on('channel_pinned_message', (message: any) => events.channel_pinned_message(message));
    // client.on('user_join', (message: any) => events.user_join(message));
    // client.on('guild_boost', (message: any) => events.guild_boost(message));
    // client.on('guild_boost_tier_1', (message: any) => events.guild_boost_tier_1(message));
    // client.on('guild_boost_tier_2', (message: any) => events.guild_boost_tier_2(message));
    // client.on('guild_boost_tier_3', (message: any) => events.guild_boost_tier_3(message));
    // client.on('channel_follow_add', (message: any) => events.channel_follow_add(message));
    // client.on('guild_discovery_disqualified', (message: any) => events.guild_discovery_disqualified(message));
    // client.on('guild_discovery_requalified', (message: any) => events.guild_discovery_requalified(message));
    // client.on('guild_discovery_grace_period_initial_warning', (message: any) => events.guild_discovery_grace_period_initial_warning(message));
    // client.on('guild_discovery_grace_period_final_warning', (message: any) => events.guild_discovery_grace_period_final_warning(message));
    // client.on('thread_created', (message: any) => events.thread_created(message));
    // client.on('reply', (message: any) => events.reply(message));
    // client.on('chat_input_command', (message: any) => events.chat_input_command(message));
    // client.on('thread_starter_message', (message: any) => events.thread_starter_message(message));
    // client.on('guild_invite_reminder', (message: any) => events.guild_invite_reminder(message));
    // client.on('context_menu_command', (message: any) => events.context_menu_command(message));
    // client.on('auto_moderation_action', (message: any) => events.auto_moderation_action(message));
    // client.on('role_subscription_purchase', (message: any) => events.role_subscription_purchase(message));
    // client.on('interaction_premium_upsell', (message: any) => events.interaction_premium_upsell(message));
    // client.on('stage_start', (message: any) => events.stage_start(message));
    // client.on('stage_end', (message: any) => events.stage_end(message));
    // client.on('stage_speaker', (message: any) => events.stage_speaker(message));
    // client.on('stage_topic', (message: any) => events.stage_topic(message));
    // client.on('guild_application_premium_subscription', (message: any) => events.guild_application_premium_subscription(message));
}

main().catch((error) => {
    console.error('❌ Fatal error in main:', error);
    process.exit(1);
});