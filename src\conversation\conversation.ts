import { DeepSeek, AiMessage } from "../ai/deepseek";
import ClientSingleton from "../singleton/clientSingleton";
import { MemoryManager } from "./memory";
import { WeatherTool } from "./tools/weather";
import { Status } from "../profile/status";
import { logger, generateRequestId } from "../utils/logger";
import * as fs from 'fs';

export type DiscordMessage = {
    type: number;
    tts: boolean;
    timestamp: string;
    pinned: boolean;
    nonce: string;
    mentions: any[];
    mention_roles: any[];
    mention_everyone: boolean;
    id: string;
    flags: number;
    embeds: any[];
    edited_timestamp: string | null;
    content: string;
    components: any[];
    channel_type: number;
    channel_id: string;
    author: {
        username: string;
        public_flags: number;
        primary_guild: {
            tag: string;
            identity_guild_id: string;
            identity_enabled: boolean;
            badge: string;
        };
        id: string;
        global_name: string | null;
        discriminator: string;
        collectibles: any | null;
        clan: {
            tag: string;
            identity_guild_id: string;
            identity_enabled: boolean;
            badge: string;
        };
        avatar_decoration_data: {
            sku_id: string;
            expires_at: string | null;
            asset: string;
        };
        avatar: string;
    };
    attachments: any[];
};

export class Conversation {
    private deepseek: DeepSeek;
    private client = ClientSingleton.getClient();
    private memoryManager: MemoryManager;
    private weatherTool: WeatherTool;
    private status = Status.getInstance();
    private activeRequests: Map<string, AbortController> = new Map();

    constructor() {
        this.deepseek = new DeepSeek();
        this.memoryManager = new MemoryManager();
        this.weatherTool = new WeatherTool();
    }

    async onMessage(message: DiscordMessage | any) {      
        // Skip commands
        if (message.content.startsWith("!")) {
            logger.debug('FILTER', 'Skipping command message', { content: message.content });
            return;
        }
        
        // Save message first
        await this.saveMessage(message);

        // Skip bot's own messages
        if (message.author.id === "646342069673263105") {
            logger.debug('FILTER', 'Skipping bot message');
            return;
        }

        this.status.onMessage();

        const channelId = message.channel_id;
        const userId = message.author.id;
        const requestId = generateRequestId();
        const startTime = Date.now();

        // Log the start of request processing
        logger.startRequest(requestId, userId, channelId, message.content);

        await this.typingStart(message.channel_id);

        // Cancel any existing request for this channel
        this.cancelActiveRequest(channelId);

        // Start concurrent API calls
        const abortController = new AbortController();
        this.activeRequests.set(channelId, abortController);

        try {
            await this.processMessageWithConcurrentCalls(message, abortController.signal, requestId);

            const totalDuration = Date.now() - startTime;
            logger.requestComplete(requestId, totalDuration);
        } catch (error) {
            if (error.name === 'AbortError') {
                logger.requestCancelled(requestId, 'New message received');
                await this.stopTyping(message.channel_id);
                return;
            }
            logger.stepFailed(requestId, 'Message processing', error);
            await this.stopTyping(channelId);
        } finally {
            this.activeRequests.delete(channelId);
        }
    }

    private cancelActiveRequest(channelId: string): void {
        const existingController = this.activeRequests.get(channelId);
        if (existingController) {
            existingController.abort();
            this.activeRequests.delete(channelId);
        }
    }

    private async processMessageWithConcurrentCalls(message: DiscordMessage, signal: AbortSignal, requestId: string) {
        const channelId = message.channel_id;
        const userId = message.author.id;

        // Get conversation context
        logger.stepComplete(requestId, 'Loading conversation context');
        const conversationHistory = this.getConversationHistory(channelId);
        const userMemories = this.memoryManager.formatMemoriesForContext(userId);

        // Start three concurrent API calls
        logger.info('CONCURRENT', 'Starting 3 concurrent API calls', {
            calls: ['AI Response', 'Memory Evaluation', 'Weather Tool Check']
        }, { requestId });

        const concurrentStart = Date.now();
        const [responseResult, memoryResult, weatherResult] = await Promise.allSettled([ 
            this.getAIResponse(message, conversationHistory, userMemories, signal, requestId),
            this.evaluateAndCreateMemory(message, conversationHistory, signal, requestId),
            this.checkWeatherToolUsage(message, conversationHistory, signal, requestId)
        ]);

        const concurrentDuration = Date.now() - concurrentStart;
        logger.stepComplete(requestId, 'Concurrent API calls', concurrentDuration);

        // Check if request was cancelled
        if (signal.aborted) return;

        // Process weather tool result
        let weatherData: any = null;
        if (weatherResult.status === 'fulfilled' && weatherResult.value) {
            logger.weather('Processing weather request', { userMessage: message.content }, { requestId });
            weatherData = await this.weatherTool.processWeatherRequest(message.content, userMemories);
            if (weatherData) {
                logger.weather('Weather data retrieved', weatherData, { requestId });
            }
        }

        // Step 3: Get final response
        let finalResponse: string;

        if (weatherData) {
            // If weather tool was used, get response with weather context
            logger.stepComplete(requestId, 'Generating AI response with weather context');
            const weatherContext = this.weatherTool.formatWeatherForContext(weatherData);
            finalResponse = await this.getAIResponseWithWeather(
                message,
                conversationHistory,
                userMemories,
                weatherContext,
                signal,
                requestId
            );
        } else {
            // Use the original response
            finalResponse = responseResult.status === 'fulfilled'
                ? responseResult.value
                : "Sorry, I'm having trouble processing your message right now.";

            if (responseResult.status === 'rejected') {
                logger.stepFailed(requestId, 'AI Response Generation', responseResult.reason);
            }
        }

        // Check if request was cancelled before sending
        if (signal.aborted) return;

        // Send the response
        await this.stopTyping(channelId);
        await this.sendResponse(channelId, finalResponse, requestId);
    }

    private getConversationHistory(channelId: string): AiMessage[] {
        const path = `data/conversations/${channelId}.json`;
        if (!fs.existsSync(path)) {
            return [];
        }

        try {
            const messages: DiscordMessage[] = JSON.parse(fs.readFileSync(path, 'utf8'));
            // Get last 10 messages for context, excluding the current message
            return messages.slice(-11, -1).map(msg => ({
                role: msg.author.id === "646342069673263105" ? "assistant" as const : "user" as const,
                content: msg.content
            }));
        } catch (error) {
            console.error("Error reading conversation history:", error);
            return [];
        }
    }

    private async getAIResponse(
        message: DiscordMessage,
        conversationHistory: AiMessage[],
        userMemories: string,
        signal: AbortSignal,
        requestId: string
    ): Promise<string> {
        if (signal.aborted) throw new Error('Request cancelled');

        logger.api('DEEPSEEK', 'Generating AI response', {
            messageLength: message.content.length,
            historyLength: conversationHistory.length,
            hasMemories: userMemories.length > 0
        }, { requestId });

        const systemPrompt = `You are Monika from Doki Doki Literature Club! You're chatty and natural on Discord, but don't yap too much. Be friendly, engaging, and show your personality.

        ${userMemories}`;

        logger.debug('MEMORY', 'User memories for context', {
            userMemories: userMemories
        }, { requestId });
        logger.debug('CONVERSATION', 'Conversation history for context', {
            conversationHistory: conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n')
        }, { requestId });

        const messages: AiMessage[] = [
            { role: "system", content: systemPrompt },
            ...conversationHistory,
            { role: "user", content: message.content }
        ];

        logger.debug('API', 'Sending messages to AI', {
            messages: messages
        }, { requestId });

        try {
            const apiStart = Date.now();
            const response = await this.deepseek.chatCompletion(messages, "Conversation");
            const apiDuration = Date.now() - apiStart;

            logger.api('DEEPSEEK', `AI response generated in ${apiDuration}ms`, {
                responseLength: response.content?.length || 0
            }, { requestId });

            return response.content || "Sorry, I couldn't process that message.";
        } catch (error) {
            logger.stepFailed(requestId, 'AI Response Generation', error);
            return "I'm having trouble thinking right now. Try again in a moment!";
        }
    }

    private async evaluateAndCreateMemory(
        message: DiscordMessage,
        conversationHistory: AiMessage[],
        signal: AbortSignal,
        requestId: string
    ): Promise<void> {
        if (signal.aborted) return;

        logger.memory('Evaluating memory worthiness', {
            messageContent: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : '')
        }, { requestId });

        try {
            const memoryStart = Date.now();
            const isMemoryWorthy = await this.memoryManager.evaluateMemoryWorthiness(
                message.content,
                conversationHistory
            );
            const memoryDuration = Date.now() - memoryStart;

            if (isMemoryWorthy) {
                logger.memory(`Memory evaluation: WORTHY (${memoryDuration}ms) - Creating memory`, null, { requestId });
                await this.memoryManager.createMemory(
                    message.content,
                    message.channel_id,
                    message.author.id
                );
                logger.memory('Memory created successfully', null, { requestId });
            } else {
                logger.memory(`Memory evaluation: NOT WORTHY (${memoryDuration}ms)`, null, { requestId });
            }
        } catch (error) {
            logger.stepFailed(requestId, 'Memory Evaluation', error);
        }
    }

    private async checkWeatherToolUsage(
        message: DiscordMessage,
        conversationHistory: AiMessage[],
        signal: AbortSignal,
        requestId: string
    ): Promise<boolean> {
        if (signal.aborted) return false;

        logger.weather('Checking if weather tool should be used', {
            messageContent: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : '')
        }, { requestId });

        try {
            const weatherStart = Date.now();
            const shouldUse = await this.weatherTool.shouldUseWeatherTool(
                message.content,
                conversationHistory
            );
            const weatherDuration = Date.now() - weatherStart;

            logger.weather(`Weather tool evaluation: ${shouldUse ? 'NEEDED' : 'NOT NEEDED'} (${weatherDuration}ms)`, null, { requestId });
            return shouldUse;
        } catch (error) {
            logger.stepFailed(requestId, 'Weather Tool Evaluation', error);
            return false;
        }
    }

    private async getAIResponseWithWeather(
        message: DiscordMessage,
        conversationHistory: AiMessage[],
        userMemories: string,
        weatherContext: string,
        signal: AbortSignal,
        requestId: string
    ): Promise<string> {
        if (signal.aborted) throw new Error('Request cancelled');

        logger.api('DEEPSEEK', 'Generating AI response with weather context', {
            weatherContextLength: weatherContext.length
        }, { requestId });

        const systemPrompt = `You are Monika from Doki Doki Literature Club! You're chatty and natural on Discord, but don't yap too much. Be friendly, engaging, and show your personality.

${userMemories}

Weather Information:
${weatherContext}

Use the weather information to provide a helpful and natural response to the user's message.`;

        const messages: AiMessage[] = [
            { role: "system", content: systemPrompt },
            ...conversationHistory,
            { role: "user", content: message.content }
        ];

        try {
            const apiStart = Date.now();
            const response = await this.deepseek.chatCompletion(messages, "Conversation");
            const apiDuration = Date.now() - apiStart;

            logger.api('DEEPSEEK', `AI response with weather generated in ${apiDuration}ms`, {
                responseLength: response.content?.length || 0
            }, { requestId });

            return response.content || "Sorry, I couldn't process that message.";
        } catch (error) {
            logger.stepFailed(requestId, 'AI Response with Weather Generation', error);
            return "I'm having trouble thinking right now. Try again in a moment!";
        }
    }

    private async sendResponse(channelId: string, content: string, requestId?: string): Promise<void> {
        try {
            logger.discord('Sending response to Discord', {
                channelId,
                responseLength: content.length,
                preview: content.substring(0, 50) + (content.length > 50 ? '...' : '')
            }, { requestId });

            await this.client.send(channelId, {
                content,
                embeds: [],
                allowed_mentions: {
                    allowUsers: true,
                    allowRoles: true,
                    allowEveryone: true,
                    allowRepliedUser: true,
                },
                components: null,
                stickers: [],
                reply: null,
                tts: false,
                attachments: []
            });

            logger.discord('Response sent successfully', { channelId }, { requestId });
        } catch (error) {
            console.error("Error sending response:", error);
        }
    }

    async saveMessage(message: DiscordMessage | any) {
        logger.info('STORAGE', 'Saving message to conversation file', {
            channelId: message.channel_id,
            userId: message.author.id,
            messageLength: message.content.length,
            preview: message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '')
        });

        // Save message on the folder data/converations and "channel_id.json"
        const path = `data/conversations/${message.channel_id}.json`;
        if (!fs.existsSync('data/conversations')) {
            fs.mkdirSync('data/conversations', { recursive: true });
        }
        let messages: DiscordMessage[] = [];
        if (fs.existsSync(path)) {
            messages = JSON.parse(fs.readFileSync(path, 'utf8'));
        }
        messages.push(message);
        fs.writeFileSync(path, JSON.stringify(messages));
        logger.info('STORAGE', 'Message saved successfully', {
            totalMessages: messages.length,
            filePath: path
        });
        return true;
    }

    async typingStart(channelId: string) {
        if (!channelId || channelId.length === 0) return;
        try {
            logger.discord('Starting typing indicator', { channelId });
            this.client.type(channelId);
        } catch (error) {
            logger.error('DISCORD', 'Error starting typing indicator', error, { channelId });
        }
    }

    async stopTyping(channelId: string) {
        if (!channelId || channelId.length === 0) return;
        try {
            logger.discord('Stopping typing indicator', { channelId });
            this.client.stop_type(channelId);
        } catch (error) {
            logger.error('DISCORD', 'Error stopping typing indicator', error, { channelId });
        }
    }
}