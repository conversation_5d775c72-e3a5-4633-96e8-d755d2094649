
import Client<PERSON>ing<PERSON> from "../singleton/clientSingleton";
import { logger } from "../utils/logger";

export class Status {
    private client = ClientSingleton.getClient();
    private idleTimer: NodeJS.Timeout | null = null;
    private readonly IDLE_TIMEOUT = 1 * 60 * 1000;
    private currentStatus: "online" | "idle" = "online";

    private static instance: Status | null = null;

    public static getInstance(): Status {
        if (!Status.instance) {
            Status.instance = new Status();
        }
        return Status.instance;
    }
    
    constructor() {
        
    }

    async onMessage() {
        if (this.idleTimer) {
            logger.info("STATUS", "Resetting idle timer due to new message");
            clearTimeout(this.idleTimer);
        }

        if (this.currentStatus === "idle") {
            logger.info("STATUS", "Changing status to online due to new message");
            await this.client.change_status("online");
            this.currentStatus = "online";
        } else {
            logger.info("STATUS", "Status is already online, no change needed");
        }

        logger.info("STATUS", "Setting new idle timer");
        this.idleTimer = setTimeout(() => {
            this.client.change_status("idle");
            logger.info("STATUS", "Changed status to idle due to inactivity");
            this.currentStatus = "idle";
            this.idleTimer = null;
        }, this.IDLE_TIMEOUT);
    }
}